<?php
/**
 * WHMCS Coolify Server Module
 *
 * This module integrates WHMCS with Coolify to automatically deploy n8n instances
 *
 * <AUTHOR> Name
 * @version 1.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use Illuminate\Database\Capsule\Manager as Capsule;

require_once __DIR__ . '/lib/CoolifyAPI.php';
require_once __DIR__ . '/templates/UserGuideTemplate.php';
require_once __DIR__ . '/templates/WelcomeEmailTemplate.php';

/**
 * Define module related meta data.
 */
function coolify_MetaData()
{
    return array(
        'DisplayName' => 'Coolify n8n',
        'APIVersion' => '1.1',
        'RequiresServer' => true,
        'DefaultNonSSLPort' => '80',
        'DefaultSSLPort' => '443',
        'ServiceSingleSignOnLabel' => 'Open n8n Dashboard',
    );
}

/**
 * Define product configuration options.
 * These are service-level settings that can vary per product/plan
 */
function coolify_ConfigOptions()
{
    return array(
        'memory_limit' => array(
            'FriendlyName' => 'Memory Limit (MB)',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '512',
            'Description' => 'Memory limit for n8n container in MB',
        ),
        'timezone' => array(
            'FriendlyName' => 'Default Timezone',
            'Type' => 'dropdown',
            'Options' => 'UTC,Europe/London,Europe/Berlin,Europe/Paris,America/New_York,America/Los_Angeles,Asia/Tokyo,Asia/Shanghai,Australia/Sydney',
            'Default' => 'UTC',
            'Description' => 'Default timezone for n8n instances',
        ),
        'n8n_image' => array(
            'FriendlyName' => 'n8n Docker Image',
            'Type' => 'text',
            'Size' => '30',
            'Default' => 'docker.n8n.io/n8nio/n8n',
            'Description' => 'Docker image to use for n8n deployments (leave default for latest official image)',
        ),
        'allow_custom_modules' => array(
            'FriendlyName' => 'Allow Custom Modules',
            'Type' => 'yesno',
            'Default' => 'yes',
            'Description' => 'Allow clients to install custom n8n community modules',
        ),
        'default_modules' => array(
            'FriendlyName' => 'Default Custom Modules',
            'Type' => 'textarea',
            'Rows' => '3',
            'Cols' => '50',
            'Default' => '',
            'Description' => 'Pre-install these custom modules (one per line, e.g., n8n-nodes-telegram)',
        ),
    );
}

/**
 * Provision a new instance of n8n.
 */
function coolify_CreateAccount($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash or Password field.';
        }

        // Get server configuration from WHMCS server settings
        $serverConfig = coolify_getServerConfiguration($params['serverid']);

        if (!$serverConfig) {
            return 'Error: Server configuration not found. Please configure the Coolify server settings.';
        }

        // Validate required server configuration
        $requiredFields = ['project_uuid', 'environment_name', 'server_uuid', 'destination_uuid'];
        foreach ($requiredFields as $field) {
            if (empty($serverConfig[$field])) {
                return "Error: Missing required server configuration: $field. Please configure the server settings.";
            }
        }

        // Get Coolify instance URL from server configuration
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);

        // Generate unique service name
        $serviceName = 'n8n-' . strtolower($params['username']) . '-' . time();

        // Get timezone from product configuration (default to UTC)
        $timezone = !empty($params['configoption2']) ? $params['configoption2'] : 'UTC';

        // Get n8n image/version from product configuration (default to latest)
        $n8nImage = !empty($params['configoption3']) ? $params['configoption3'] : 'docker.n8n.io/n8nio/n8n';
        
        // Extract version from image name
        $n8nVersion = 'latest';
        if (strpos($n8nImage, ':') !== false) {
            list($imageName, $n8nVersion) = explode(':', $n8nImage, 2);
        }

        // Get default custom modules from configuration
        $defaultModules = array();
        if (!empty($params['configoption5'])) {
            $modulesList = explode("\n", trim($params['configoption5']));
            $defaultModules = array_filter(array_map('trim', $modulesList));
        }

        // Generate n8n Docker Compose configuration using official template
        $dockerCompose = generateN8nDockerCompose($serviceName, $timezone, $n8nVersion, $defaultModules);

        // Prepare service configuration with Docker Compose
        $serviceConfig = array(
            'name' => $serviceName,
            'description' => 'n8n instance for ' . $params['clientsdetails']['firstname'] . ' ' . $params['clientsdetails']['lastname'],
            'project_uuid' => $serverConfig['project_uuid'],
            'environment_name' => $serverConfig['environment_name'],
            'server_uuid' => $serverConfig['server_uuid'],
            'destination_uuid' => $serverConfig['destination_uuid'],
            'docker_compose_raw' => $dockerCompose,
            'instant_deploy' => true
        );
        
        // Log the service configuration for debugging
        logModuleCall(
            'coolify',
            'CreateAccount_Request',
            $serviceConfig,
            'Sending service creation request',
            '',
            array($apiToken) // Hide API token from logs
        );

        // Create the service
        $result = $api->createService($serviceConfig);

        if ($result && isset($result['uuid'])) {
            // Store service details in WHMCS
            $serviceUuid = $result['uuid'];

            // Get the service domain from Coolify (will be generated automatically)
            $serviceDomain = 'Pending'; // Will be updated once service is deployed
            if (isset($result['fqdn']) && !empty($result['fqdn'])) {
                $serviceDomain = $result['fqdn'];
            } elseif (isset($result['domains']) && !empty($result['domains'])) {
                $serviceDomain = is_array($result['domains']) ? $result['domains'][0] : $result['domains'];
            }

            // Update the service hostname and other details in WHMCS
            $pdo = Capsule::connection()->getPdo();
            $stmt = $pdo->prepare("UPDATE tblhosting SET domain = ?, username = ?, password = ? WHERE id = ?");
            $stmt->execute([$serviceDomain, 'n8n-user', 'Generated by Coolify', $params['serviceid']]);

            // Store additional data in custom fields or notes
            logModuleCall(
                'coolify',
                'CreateAccount_Success',
                $serviceConfig,
                $result,
                '',
                array($apiToken) // Hide API token from logs
            );

            // Store the service UUID for future reference
            // First, ensure the custom field exists
            $fieldId = ensureCustomFieldExists($params['pid'], 'coolify_service_uuid', 'Coolify Service UUID');

            if ($fieldId) {
                // Check if value already exists
                $stmt = $pdo->prepare("SELECT id FROM tblcustomfieldsvalues WHERE fieldid = ? AND relid = ?");
                $stmt->execute([$fieldId, $params['serviceid']]);
                $existing = $stmt->fetch();

                if ($existing) {
                    // Update existing value
                    $stmt = $pdo->prepare("UPDATE tblcustomfieldsvalues SET value = ? WHERE fieldid = ? AND relid = ?");
                    $stmt->execute([$serviceUuid, $fieldId, $params['serviceid']]);
                } else {
                    // Insert new value
                    $stmt = $pdo->prepare("INSERT INTO tblcustomfieldsvalues (fieldid, relid, value) VALUES (?, ?, ?)");
                    $stmt->execute([$fieldId, $params['serviceid'], $serviceUuid]);
                }
            }

            // Store default custom modules if any were configured
            if (!empty($defaultModules)) {
                coolify_saveCustomModules($params['serviceid'], $defaultModules);
            }

            // Send welcome email to customer
            try {
                coolify_sendWelcomeEmail($params, $result);
            } catch (Exception $e) {
                // Log email error but don't fail the provisioning
                logModuleCall('coolify', 'WelcomeEmail_Error', $params, $e->getMessage(), '');
            }

            return 'success';
        } else {
            // Enhanced error reporting
            $errorMessage = 'Failed to create service';
            if (isset($result['message'])) {
                $errorMessage .= ': ' . $result['message'];
            } elseif (isset($result['errors'])) {
                $errorMessage .= ': ' . json_encode($result['errors']);
            } elseif (is_array($result)) {
                $errorMessage .= ': ' . json_encode($result);
            } else {
                $errorMessage .= ': Unknown error';
            }

            // Log the error for debugging
            logModuleCall(
                'coolify',
                'CreateAccount_Error',
                $serviceConfig,
                $result,
                $errorMessage,
                array($apiToken)
            );

            return $errorMessage;
        }
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'CreateAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Suspend an instance.
 */
function coolify_SuspendAccount($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash or Password field.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->stopService($serviceUuid);
        
        logModuleCall(
            'coolify',
            'SuspendAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'SuspendAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Unsuspend an instance.
 */
function coolify_UnsuspendAccount($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash or Password field.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->startService($serviceUuid);
        
        logModuleCall(
            'coolify',
            'UnsuspendAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'UnsuspendAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Terminate an instance.
 */
function coolify_TerminateAccount($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash or Password field.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->deleteService($serviceUuid);
        
        // Clean up custom field data
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("DELETE FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = (
            SELECT id FROM tblcustomfields WHERE fieldname = 'coolify_service_uuid' AND type = 'product' AND relid = ? LIMIT 1
        )");
        $stmt->execute([$params['serviceid'], $params['pid']]);
        
        logModuleCall(
            'coolify',
            'TerminateAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'TerminateAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Test connection to Coolify API.
 */
function coolify_TestConnection($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return array(
                'success' => false,
                'error' => 'No API token configured. Please set the Coolify API token in the server Access Hash field.',
            );
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $result = $api->getServers();
        
        if ($result && is_array($result)) {
            $success = true;
            $errorMsg = 'Connection successful. Found ' . count($result) . ' server(s).';
        } else {
            $success = false;
            $errorMsg = 'Failed to connect or retrieve servers.';
        }
        
    } catch (Exception $e) {
        $success = false;
        $errorMsg = 'Connection failed: ' . $e->getMessage();
    }
    
    return array(
        'success' => $success,
        'error' => $errorMsg,
    );
}

/**
 * Admin area output.
 */
function coolify_AdminCustomButtonArray($params)
{
    $buttons = array(
        'View n8n Service' => 'viewService',
        'Restart n8n' => 'restartService',
        'View Service Logs' => 'viewLogs',
        'Update n8n Version' => 'updateN8nVersion',
    );

    // Add custom modules button if enabled
    $allowCustomModules = !empty($params['configoption4']) && $params['configoption4'] == 'on';
    if ($allowCustomModules) {
        $buttons['Manage Custom Modules'] = 'manageCustomModulesAdmin';
    }

    return $buttons;
}

/**
 * Client area custom buttons.
 */
function coolify_ClientAreaCustomButtonArray($params)
{
    $serviceUuid = getServiceUuid($params['serviceid']);

    // Always provide Update Version button, even if other checks fail
    $buttons = array();
    $buttons['Update n8n Version'] = 'updateN8nVersionClient';

    // Add custom modules button if enabled
    $allowCustomModules = !empty($params['configoption4']) && $params['configoption4'] == 'on';
    if ($allowCustomModules) {
        $buttons['Manage Custom Modules'] = 'manageCustomModules';
    }

    if (!$serviceUuid) {
        return $buttons;
    }

    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return $buttons;
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        // Validate required server configuration fields
        if (empty($serverConfig['project_uuid']) || empty($serverConfig['server_uuid'])) {
            return 'Error: Server configuration is incomplete. Please ensure project_uuid and server_uuid are configured in the server settings.';
        }

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceInfo = $api->getService($serviceUuid);

        if ($serviceInfo) {
            $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';

            if ($status == 'running') {
                $buttons['Stop Service'] = 'stopService';
                $buttons['Restart Service'] = 'restartService';
            } elseif ($status == 'stopped') {
                $buttons['Start Service'] = 'startService';
            } else {
                $buttons['Restart Service'] = 'restartService';
            }
        }
    } catch (Exception $e) {
        logModuleCall('coolify', 'ClientAreaCustomButtonArray', $params, $e->getMessage(), '');
    }

    return $buttons;
}

/**
 * Client area output - Enhanced service management dashboard.
 */
function coolify_ClientArea($params)
{
    // Check if this is the update version page
    if (isset($_GET['modop']) && $_GET['modop'] === 'custom' && isset($_GET['a']) && $_GET['a'] === 'updateN8nVersionClient') {
        return coolify_renderClientUpdateVersionForm($params);
    }

    // Check if this is the custom modules page
    if (isset($_GET['modop']) && $_GET['modop'] === 'custom' && isset($_GET['a']) && $_GET['a'] === 'manageCustomModules') {
        return coolify_renderCustomModulesForm($params);
    }

    $serviceUuid = getServiceUuid($params['serviceid']);

    if (!$serviceUuid) {
        return coolify_renderProvisioningTemplate();
    }

    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured. Please contact support.</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        // Validate required server configuration fields
        if (empty($serverConfig['project_uuid']) || empty($serverConfig['server_uuid'])) {
            return 'Error: Server configuration is incomplete. Please ensure project_uuid and server_uuid are configured in the server settings.';
        }

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceInfo = $api->getService($serviceUuid);

        if ($serviceInfo) {
            return coolify_renderServiceDashboard($params, $serviceInfo);
        } else {
            return coolify_renderServiceUnavailable($params);
        }

    } catch (Exception $e) {
        return coolify_renderServiceError($params, $e);
    }
}

/**
 * Single Sign-On function.
 */
function coolify_ServiceSingleSignOn($params)
{
    return array(
        'success' => true,
        'redirectTo' => 'https://' . $params['domain'],
    );
}

/**
 * Start service action for client area.
 */
function coolify_startService($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: API token not configured. Please contact support.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);

        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }

        $result = $api->startService($serviceUuid);

        logModuleCall(
            'coolify',
            'StartService_Client',
            $params,
            $result,
            '',
            array($apiToken)
        );

        return 'success';

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'StartService_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Stop service action for client area.
 */
function coolify_stopService($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: API token not configured. Please contact support.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);

        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }

        $result = $api->stopService($serviceUuid);

        logModuleCall(
            'coolify',
            'StopService_Client',
            $params,
            $result,
            '',
            array($apiToken)
        );

        return 'success';

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'StopService_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Restart service action for client area.
 */
function coolify_restartService($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: API token not configured. Please contact support.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);

        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }

        $result = $api->restartService($serviceUuid);

        logModuleCall(
            'coolify',
            'RestartService_Client',
            $params,
            $result,
            '',
            array($apiToken)
        );

        return 'success';

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'RestartService_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Manage custom n8n modules for client area.
 */
function coolify_manageCustomModules($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: Service configuration error. Please contact support.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return 'Error: Service not found';
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            $action = $_POST['action'];

            if ($action === 'install_module' && isset($_POST['module_name'])) {
                $moduleName = trim($_POST['module_name']);

                // Validate module name
                if (empty($moduleName)) {
                    return 'Error: Module name cannot be empty';
                }

                // Basic validation for npm package names
                if (!preg_match('/^[a-z0-9]([a-z0-9\-_])*$/', $moduleName)) {
                    return 'Error: Invalid module name. Use only lowercase letters, numbers, hyphens, and underscores.';
                }

                $result = coolify_installCustomModule($params, $serviceUuid, $moduleName);

                if ($result === 'success') {
                    return 'success';
                } else {
                    return $result;
                }
            } elseif ($action === 'uninstall_module' && isset($_POST['module_name'])) {
                $moduleName = trim($_POST['module_name']);

                $result = coolify_uninstallCustomModule($params, $serviceUuid, $moduleName);

                if ($result === 'success') {
                    return 'success';
                } else {
                    return $result;
                }
            }
        } else {
            // Return a form for client area display
            return 'redirect';
        }

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'ManageCustomModules_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Update n8n version for client area.
 */
function coolify_updateN8nVersionClient($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return 'Error: Service configuration error. Please contact support.';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return 'Error: Service not found';
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['n8n_version'])) {
            $newVersion = $_POST['n8n_version'];
            
            // Get current service info
            $serviceInfo = $api->getService($serviceUuid);
            
            // Get timezone from params or use default
            $timezone = !empty($params['configoption2']) ? $params['configoption2'] : 'UTC';

            // Get current custom modules to preserve them
            $currentModules = coolify_getCurrentCustomModules($params['serviceid']);

            // Generate new Docker Compose with updated version
            $dockerCompose = generateN8nDockerCompose($serviceInfo['name'], $timezone, $newVersion, $currentModules);
            
            // Update the service with required fields
            $updateData = [
                'docker_compose_raw' => base64_encode($dockerCompose),
                'project_uuid' => $serverConfig['project_uuid'],
                'server_uuid' => $serverConfig['server_uuid']
            ];
            
            $result = $api->updateService($serviceUuid, $updateData);
            
            if ($result) {
                // Restart the service to apply changes
                $api->restartService($serviceUuid);
                
                logModuleCall(
                    'coolify',
                    'UpdateN8nVersion_Client',
                    ['version' => $newVersion, 'serviceUuid' => $serviceUuid, 'userid' => $params['userid']],
                    $result,
                    '',
                    array($apiToken)
                );
                
                return 'success';
            } else {
                return 'Error: Failed to update n8n version';
            }
        } else {
            // Return a form for client area display
            return 'redirect';
        }
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'UpdateN8nVersion_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Manage custom n8n modules for admin area.
 */
function coolify_manageCustomModulesAdmin($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured. Please set the Coolify API token in the server Access Hash or Password field.</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return '<div class="alert alert-danger">Service UUID not found</div>';
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            $action = $_POST['action'];

            if ($action === 'install_module' && isset($_POST['module_name'])) {
                $moduleName = trim($_POST['module_name']);

                // Validate module name
                if (empty($moduleName)) {
                    return '<div class="alert alert-danger">Module name cannot be empty</div>';
                }

                // Basic validation for npm package names
                if (!preg_match('/^[a-z0-9]([a-z0-9\-_])*$/', $moduleName)) {
                    return '<div class="alert alert-danger">Invalid module name. Use only lowercase letters, numbers, hyphens, and underscores.</div>';
                }

                $result = coolify_installCustomModule($params, $serviceUuid, $moduleName);

                if ($result === 'success') {
                    return '<div class="alert alert-success">Custom module "' . htmlspecialchars($moduleName) . '" installed successfully. The service is being restarted.</div>';
                } else {
                    return '<div class="alert alert-danger">' . htmlspecialchars($result) . '</div>';
                }
            } elseif ($action === 'uninstall_module' && isset($_POST['module_name'])) {
                $moduleName = trim($_POST['module_name']);

                $result = coolify_uninstallCustomModule($params, $serviceUuid, $moduleName);

                if ($result === 'success') {
                    return '<div class="alert alert-success">Custom module "' . htmlspecialchars($moduleName) . '" uninstalled successfully. The service is being restarted.</div>';
                } else {
                    return '<div class="alert alert-danger">' . htmlspecialchars($result) . '</div>';
                }
            }
        }

        // Get current custom modules
        $currentModules = coolify_getCurrentCustomModules($params['serviceid']);

        // Display management interface
        $output = '<style>
        .n8n-admin-modules { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .n8n-admin-card { background: white; border-radius: 16px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .n8n-form-group { margin: 25px 0; }
        .n8n-form-label { display: block; color: #2d3748; font-weight: 600; margin-bottom: 8px; font-size: 16px; }
        .n8n-form-input { width: 100%; padding: 12px 16px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 16px; transition: border-color 0.2s; }
        .n8n-form-input:focus { outline: none; border-color: #667eea; }
        .n8n-help-text { color: #718096; font-size: 14px; margin-top: 5px; }
        .n8n-btn { padding: 14px 28px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; font-size: 16px; transition: all 0.2s; text-decoration: none; display: inline-block; }
        .n8n-btn-primary { background: #667eea; color: white; }
        .n8n-btn-primary:hover { background: #5a67d8; color: white; text-decoration: none; transform: translateY(-1px); }
        .n8n-btn-danger { background: #fc8181; color: white; padding: 8px 16px; font-size: 14px; }
        .n8n-btn-danger:hover { background: #f56565; color: white; text-decoration: none; transform: translateY(-1px); }
        .n8n-module-item { background: #f7fafc; border-radius: 8px; padding: 15px; margin: 10px 0; display: flex; justify-content: space-between; align-items: center; }
        .n8n-module-name { font-weight: 600; color: #2d3748; }
        </style>';

        $output .= '<div class="n8n-admin-modules">';
        $output .= '<div class="n8n-admin-card">';

        // Header
        $output .= '<h2 style="margin: 0 0 20px 0; color: #2d3748;"><i class="fa fa-puzzle-piece" style="color: #667eea;"></i> Manage Custom n8n Modules</h2>';

        // Show currently installed modules
        if (!empty($currentModules)) {
            $output .= '<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Installed Modules (' . count($currentModules) . ')</h3>';
            foreach ($currentModules as $module) {
                $output .= '<div class="n8n-module-item">';
                $output .= '<span class="n8n-module-name">' . htmlspecialchars($module) . '</span>';
                $output .= '<form method="post" action="" style="margin: 0;">';
                $output .= '<input type="hidden" name="action" value="uninstall_module">';
                $output .= '<input type="hidden" name="module_name" value="' . htmlspecialchars($module) . '">';
                $output .= '<button type="submit" class="n8n-btn n8n-btn-danger" onclick="return confirm(\'Uninstall ' . htmlspecialchars($module) . '? This will restart the service.\');">';
                $output .= '<i class="fa fa-trash"></i> Uninstall';
                $output .= '</button>';
                $output .= '</form>';
                $output .= '</div>';
            }
        } else {
            $output .= '<p style="color: #a0aec0; font-style: italic; margin: 20px 0;">No custom modules installed.</p>';
        }

        // Install new module form
        $output .= '<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Install New Module</h3>';
        $output .= '<form method="post" action="">';
        $output .= '<input type="hidden" name="action" value="install_module">';

        $output .= '<div class="n8n-form-group">';
        $output .= '<label class="n8n-form-label" for="module_name">Module Name</label>';
        $output .= '<input type="text" name="module_name" id="module_name" class="n8n-form-input" placeholder="e.g., n8n-nodes-telegram" required>';
        $output .= '<div class="n8n-help-text">Enter the npm package name of the n8n community module to install.</div>';
        $output .= '</div>';

        $output .= '<div style="margin-top: 30px;">';
        $output .= '<button type="submit" class="n8n-btn n8n-btn-primary" onclick="return confirm(\'Install this module? The service will restart automatically.\');">';
        $output .= '<i class="fa fa-plus"></i> Install Module';
        $output .= '</button>';
        $output .= '</div>';
        $output .= '</form>';

        $output .= '</div>';
        $output .= '</div>';

        return $output;

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'ManageCustomModulesAdmin',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Update n8n version for an existing service.
 */
function coolify_updateN8nVersion($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured. Please set the Coolify API token in the server Access Hash or Password field.</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        // Validate required server configuration fields
        if (empty($serverConfig['project_uuid']) || empty($serverConfig['server_uuid'])) {
            return '<div class="alert alert-danger">Server configuration is incomplete. Please ensure project_uuid and server_uuid are configured in the server settings.</div>';
        }

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return '<div class="alert alert-danger">Service UUID not found</div>';
        }

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['n8n_version'])) {
            $newVersion = $_POST['n8n_version'];
            
            // Get current service info
            $serviceInfo = $api->getService($serviceUuid);
            
            // Get timezone from params or use default
            $timezone = !empty($params['configoption2']) ? $params['configoption2'] : 'UTC';

            // Get current custom modules to preserve them
            $currentModules = coolify_getCurrentCustomModules($params['serviceid']);

            // Generate new Docker Compose with updated version
            $dockerCompose = generateN8nDockerCompose($serviceInfo['name'], $timezone, $newVersion, $currentModules);
            
            // Update the service with required fields
            $updateData = [
                'docker_compose_raw' => base64_encode($dockerCompose),
                'project_uuid' => $serverConfig['project_uuid'],
                'server_uuid' => $serverConfig['server_uuid']
            ];
            
            $result = $api->updateService($serviceUuid, $updateData);
            
            if ($result) {
                // Restart the service to apply changes
                $api->restartService($serviceUuid);
                
                logModuleCall(
                    'coolify',
                    'UpdateN8nVersion',
                    ['version' => $newVersion, 'serviceUuid' => $serviceUuid],
                    $result,
                    '',
                    array($apiToken)
                );
                
                return '<div class="alert alert-success">n8n version updated successfully to ' . htmlspecialchars($newVersion) . '. The service is being restarted with the new version.</div>';
            } else {
                return '<div class="alert alert-danger">Failed to update n8n version</div>';
            }
        }

        // Display version selection form
        $output = '<style>
        .n8n-admin-update { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .n8n-admin-card { background: white; border-radius: 16px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .n8n-version-badge { display: inline-block; background: #e6f7ff; color: #1890ff; padding: 8px 16px; border-radius: 20px; font-weight: 600; font-size: 16px; }
        .n8n-form-group { margin: 25px 0; }
        .n8n-form-label { display: block; color: #2d3748; font-weight: 600; margin-bottom: 8px; font-size: 16px; }
        .n8n-form-select { width: 100%; padding: 12px 16px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 16px; transition: border-color 0.2s; }
        .n8n-form-select:focus { outline: none; border-color: #667eea; }
        .n8n-help-text { color: #718096; font-size: 14px; margin-top: 5px; }
        .n8n-warning-box { background: #fef5e7; border: 1px solid #f6ad55; border-radius: 12px; padding: 20px; margin: 25px 0; }
        .n8n-warning-title { color: #d97706; font-weight: 600; margin-bottom: 10px; font-size: 16px; }
        .n8n-warning-list { color: #92400e; margin: 0; padding-left: 20px; }
        .n8n-warning-list li { margin: 8px 0; }
        .n8n-btn { padding: 14px 28px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; font-size: 16px; transition: all 0.2s; text-decoration: none; display: inline-block; }
        .n8n-btn-primary { background: #667eea; color: white; }
        .n8n-btn-primary:hover { background: #5a67d8; color: white; text-decoration: none; transform: translateY(-1px); }
        .n8n-btn-secondary { background: #e2e8f0; color: #4a5568; margin-left: 10px; }
        .n8n-btn-secondary:hover { background: #cbd5e0; color: #2d3748; text-decoration: none; }
        .n8n-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 25px 0; }
        .n8n-info-item { background: #f7fafc; border-radius: 8px; padding: 15px; text-align: center; }
        .n8n-info-label { color: #718096; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; }
        .n8n-info-value { font-size: 16px; font-weight: 600; color: #2d3748; margin-top: 5px; }
        .n8n-resources { background: #f8fafc; border-radius: 12px; padding: 20px; margin-top: 25px; }
        .n8n-resources h4 { color: #2d3748; margin: 0 0 15px 0; }
        .n8n-resources ul { margin: 0; padding-left: 20px; }
        .n8n-resources li { margin: 8px 0; }
        .n8n-resources a { color: #667eea; text-decoration: none; }
        .n8n-resources a:hover { text-decoration: underline; }
        </style>';
        
        $output .= '<div class="n8n-admin-update">';
        $output .= '<div class="n8n-admin-card">';
        
        // Header
        $output .= '<h2 style="margin: 0 0 20px 0; color: #2d3748;"><i class="fa fa-refresh" style="color: #667eea;"></i> Update n8n Version</h2>';
        
        // Get current service info
        try {
            $serviceInfo = $api->getService($serviceUuid);
            $currentVersion = 'Unknown';
            
            // Try to extract current version from docker compose
            if (isset($serviceInfo['docker_compose_raw'])) {
                $dockerCompose = base64_decode($serviceInfo['docker_compose_raw']);
                if (preg_match('/image:\s*docker\.n8n\.io\/n8nio\/n8n:?([^\s]*)/', $dockerCompose, $matches)) {
                    $currentVersion = !empty($matches[1]) ? $matches[1] : 'latest';
                }
            }
            
            $output .= '<p style="color: #718096; margin-bottom: 20px;">Current version: <span class="n8n-version-badge">' . htmlspecialchars($currentVersion) . '</span></p>';
        } catch (Exception $e) {
            $output .= '<p style="color: #e53e3e; margin-bottom: 20px;"><i class="fa fa-exclamation-triangle"></i> Unable to determine current version</p>';
        }
        
        $output .= '<form method="post" action="">';
        
        $output .= '<div class="n8n-form-group">';
        $output .= '<label class="n8n-form-label" for="n8n_version">Select New Version</label>';
        $output .= '<select name="n8n_version" id="n8n_version" class="n8n-form-select">';
        $output .= '<option value="latest">Latest (Recommended)</option>';
        
        // Fetch available n8n versions
        $availableVersions = coolify_fetchN8nVersions();
        foreach ($availableVersions as $version) {
            $selected = (isset($currentVersion) && $currentVersion === $version) ? ' selected' : '';
            $isCurrent = (isset($currentVersion) && $currentVersion === $version) ? ' (Current)' : '';
            $output .= '<option value="' . htmlspecialchars($version) . '"' . $selected . '>' . htmlspecialchars($version) . $isCurrent . '</option>';
        }
        
        $output .= '</select>';
        $output .= '<div class="n8n-help-text">Select the n8n version you want to update to. Using "latest" will always pull the most recent stable version.</div>';
        $output .= '</div>';
        
        $output .= '<div class="n8n-warning-box">';
        $output .= '<div class="n8n-warning-title"><i class="fa fa-exclamation-triangle"></i> Important Notes</div>';
        $output .= '<ul class="n8n-warning-list">';
        $output .= '<li>Updating n8n version will restart the service</li>';
        $output .= '<li>Customer workflows and data will be preserved</li>';
        $output .= '<li>Check <a href="https://github.com/n8n-io/n8n/releases" target="_blank" style="color: #d97706;">release notes</a> for breaking changes</li>';
        $output .= '<li>Downgrading to older versions may cause compatibility issues</li>';
        $output .= '</ul>';
        $output .= '</div>';
        
        $output .= '<div style="margin-top: 30px;">';
        $output .= '<button type="submit" class="n8n-btn n8n-btn-primary" onclick="return confirm(\'Update n8n version? This will restart the service.\');">';
        $output .= '<i class="fa fa-refresh"></i> Update n8n Version';
        $output .= '</button>';
        $output .= '<a href="clientsservices.php?userid=' . $params['userid'] . '&id=' . $params['serviceid'] . '" class="n8n-btn n8n-btn-secondary">Cancel</a>';
        $output .= '</div>';
        $output .= '</form>';
        
        $output .= '</div>';
        
        // Resources section
        $output .= '<div class="n8n-resources">';
        $output .= '<h4><i class="fa fa-book"></i> Additional Resources</h4>';
        $output .= '<ul>';
        $output .= '<li><a href="https://github.com/n8n-io/n8n/releases" target="_blank">View n8n Release Notes</a></li>';
        $output .= '<li><a href="https://docs.n8n.io/breaking-changes/" target="_blank">Breaking Changes Documentation</a></li>';
        $output .= '<li><a href="https://docs.n8n.io/hosting/updating/" target="_blank">n8n Update Guide</a></li>';
        $output .= '</ul>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        return $output;
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'UpdateN8nVersion',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Helper function to get service UUID.
 */
function getServiceUuid($serviceId)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get the product ID for this service
        $stmt = $pdo->prepare("SELECT packageid FROM tblhosting WHERE id = ?");
        $stmt->execute([$serviceId]);
        $service = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service) {
            return false;
        }

        // Get the custom field ID
        $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = 'coolify_service_uuid'");
        $stmt->execute([$service['packageid']]);
        $field = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$field) {
            return false;
        }

        // Get the value
        $stmt = $pdo->prepare("SELECT value FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = ?");
        $stmt->execute([$serviceId, $field['id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['value'] : false;
    } catch (Exception $e) {
        logModuleCall('coolify', 'getServiceUuid', ['serviceId' => $serviceId], $e->getMessage(), '');
        return false;
    }
}

/**
 * Generate random password.
 */
function generateRandomPassword($length = 16)
{
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';

    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $password;
}

/**
 * Get current custom modules for a service
 */
function coolify_getCurrentCustomModules($serviceId)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get the product ID for this service
        $stmt = $pdo->prepare("SELECT packageid FROM tblhosting WHERE id = ?");
        $stmt->execute([$serviceId]);
        $service = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service) {
            return array();
        }

        // Get the custom field ID for modules
        $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = 'coolify_custom_modules'");
        $stmt->execute([$service['packageid']]);
        $field = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$field) {
            return array();
        }

        // Get the value
        $stmt = $pdo->prepare("SELECT value FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = ?");
        $stmt->execute([$serviceId, $field['id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && !empty($result['value'])) {
            return json_decode($result['value'], true) ?: array();
        }

        return array();
    } catch (Exception $e) {
        logModuleCall('coolify', 'getCurrentCustomModules', ['serviceId' => $serviceId], $e->getMessage(), '');
        return array();
    }
}

/**
 * Save custom modules for a service
 */
function coolify_saveCustomModules($serviceId, $modules)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get the product ID for this service
        $stmt = $pdo->prepare("SELECT packageid FROM tblhosting WHERE id = ?");
        $stmt->execute([$serviceId]);
        $service = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$service) {
            return false;
        }

        // Ensure the custom field exists
        $fieldId = ensureCustomFieldExists($service['packageid'], 'coolify_custom_modules', 'Coolify Custom Modules');

        if ($fieldId) {
            $modulesJson = json_encode($modules);

            // Check if value already exists
            $stmt = $pdo->prepare("SELECT id FROM tblcustomfieldsvalues WHERE fieldid = ? AND relid = ?");
            $stmt->execute([$fieldId, $serviceId]);
            $existing = $stmt->fetch();

            if ($existing) {
                // Update existing value
                $stmt = $pdo->prepare("UPDATE tblcustomfieldsvalues SET value = ? WHERE fieldid = ? AND relid = ?");
                $stmt->execute([$modulesJson, $fieldId, $serviceId]);
            } else {
                // Insert new value
                $stmt = $pdo->prepare("INSERT INTO tblcustomfieldsvalues (fieldid, relid, value) VALUES (?, ?, ?)");
                $stmt->execute([$fieldId, $serviceId, $modulesJson]);
            }

            return true;
        }

        return false;
    } catch (Exception $e) {
        logModuleCall('coolify', 'saveCustomModules', ['serviceId' => $serviceId, 'modules' => $modules], $e->getMessage(), '');
        return false;
    }
}

/**
 * Generate n8n Docker Compose configuration using official template
 * This uses SQLite database (default) for simplicity
 */
function generateN8nDockerCompose($serviceName = 'n8n', $timezone = 'UTC', $n8nVersion = 'latest', $customModules = array())
{
    // Use specific version or latest
    $n8nImage = ($n8nVersion === 'latest') ? 'docker.n8n.io/n8nio/n8n' : "docker.n8n.io/n8nio/n8n:{$n8nVersion}";

    // Prepare environment variables
    $envVars = array(
        'SERVICE_FQDN_N8N_5678',
        "'N8N_EDITOR_BASE_URL=\${SERVICE_FQDN_N8N}'",
        "'WEBHOOK_URL=\${SERVICE_FQDN_N8N}'",
        "'N8N_HOST=\${SERVICE_URL_N8N}'",
        "'GENERIC_TIMEZONE=\${GENERIC_TIMEZONE:-{$timezone}}'",
        "'TZ=\${TZ:-{$timezone}}'",
        'N8N_SECURE_COOKIE=false'
    );

    // Add custom modules environment variable if modules are specified
    if (!empty($customModules)) {
        $modulesList = implode(' ', $customModules);
        $envVars[] = "'N8N_NODES_INCLUDE={$modulesList}'";
    }

    // Format environment variables for Docker Compose
    $envString = '';
    foreach ($envVars as $env) {
        $envString .= "      - {$env}\n";
    }
    $envString = rtrim($envString, "\n");

    // Use official n8n Docker Compose template with SQLite (default database)
    $dockerCompose = "services:
  n8n:
    image: {$n8nImage}
    environment:
{$envString}
    volumes:
      - 'n8n-data:/home/<USER>/.n8n'
    healthcheck:
      test:
        - CMD-SHELL
        - 'wget -qO- http://127.0.0.1:5678/'
      interval: 5s
      timeout: 20s
      retries: 10";

    return $dockerCompose;
}

/**
 * Install a custom n8n module
 */
function coolify_installCustomModule($params, $serviceUuid, $moduleName)
{
    try {
        // Get API token and server configuration
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);

        // Get current service info
        $serviceInfo = $api->getService($serviceUuid);

        // Get current custom modules
        $currentModules = coolify_getCurrentCustomModules($params['serviceid']);

        // Add new module if not already installed
        if (!in_array($moduleName, $currentModules)) {
            $currentModules[] = $moduleName;
        }

        // Get timezone from params or use default
        $timezone = !empty($params['configoption2']) ? $params['configoption2'] : 'UTC';

        // Get current n8n version
        $n8nVersion = 'latest';
        if (isset($serviceInfo['docker_compose_raw'])) {
            $dockerCompose = base64_decode($serviceInfo['docker_compose_raw']);
            if (preg_match('/image:\s*docker\.n8n\.io\/n8nio\/n8n:?([^\s]*)/', $dockerCompose, $matches)) {
                $n8nVersion = !empty($matches[1]) ? $matches[1] : 'latest';
            }
        }

        // Generate new Docker Compose with updated modules
        $dockerCompose = generateN8nDockerCompose($serviceInfo['name'], $timezone, $n8nVersion, $currentModules);

        // Update the service
        $updateData = [
            'docker_compose_raw' => base64_encode($dockerCompose),
            'project_uuid' => $serverConfig['project_uuid'],
            'server_uuid' => $serverConfig['server_uuid']
        ];

        $result = $api->updateService($serviceUuid, $updateData);

        if ($result) {
            // Store updated modules list
            coolify_saveCustomModules($params['serviceid'], $currentModules);

            // Restart the service to apply changes
            $api->restartService($serviceUuid);

            logModuleCall(
                'coolify',
                'InstallCustomModule_Client',
                ['module' => $moduleName, 'serviceUuid' => $serviceUuid, 'userid' => $params['userid']],
                $result,
                '',
                array($apiToken)
            );

            return 'success';
        } else {
            return 'Error: Failed to install custom module';
        }

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'InstallCustomModule_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Uninstall a custom n8n module
 */
function coolify_uninstallCustomModule($params, $serviceUuid, $moduleName)
{
    try {
        // Get API token and server configuration
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);

        // Get current service info
        $serviceInfo = $api->getService($serviceUuid);

        // Get current custom modules
        $currentModules = coolify_getCurrentCustomModules($params['serviceid']);

        // Remove module from list
        $currentModules = array_filter($currentModules, function($module) use ($moduleName) {
            return $module !== $moduleName;
        });
        $currentModules = array_values($currentModules); // Re-index array

        // Get timezone from params or use default
        $timezone = !empty($params['configoption2']) ? $params['configoption2'] : 'UTC';

        // Get current n8n version
        $n8nVersion = 'latest';
        if (isset($serviceInfo['docker_compose_raw'])) {
            $dockerCompose = base64_decode($serviceInfo['docker_compose_raw']);
            if (preg_match('/image:\s*docker\.n8n\.io\/n8nio\/n8n:?([^\s]*)/', $dockerCompose, $matches)) {
                $n8nVersion = !empty($matches[1]) ? $matches[1] : 'latest';
            }
        }

        // Generate new Docker Compose with updated modules
        $dockerCompose = generateN8nDockerCompose($serviceInfo['name'], $timezone, $n8nVersion, $currentModules);

        // Update the service
        $updateData = [
            'docker_compose_raw' => base64_encode($dockerCompose),
            'project_uuid' => $serverConfig['project_uuid'],
            'server_uuid' => $serverConfig['server_uuid']
        ];

        $result = $api->updateService($serviceUuid, $updateData);

        if ($result) {
            // Store updated modules list
            coolify_saveCustomModules($params['serviceid'], $currentModules);

            // Restart the service to apply changes
            $api->restartService($serviceUuid);

            logModuleCall(
                'coolify',
                'UninstallCustomModule_Client',
                ['module' => $moduleName, 'serviceUuid' => $serviceUuid, 'userid' => $params['userid']],
                $result,
                '',
                array($apiToken)
            );

            return 'success';
        } else {
            return 'Error: Failed to uninstall custom module';
        }

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'UninstallCustomModule_Client',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return 'Error: ' . $e->getMessage();
    }
}

// ============================================================================
// CLIENT AREA TEMPLATE FUNCTIONS
// ============================================================================

/**
 * Render provisioning template when service is being set up
 */
function coolify_renderProvisioningTemplate()
{
    $output = '<style>
    .n8n-provisioning { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; text-align: center; padding: 60px 20px; }
    .n8n-provisioning-card { background: white; border-radius: 16px; padding: 40px; max-width: 600px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
    .n8n-spinner { display: inline-block; width: 60px; height: 60px; border: 4px solid #e2e8f0; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20px; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    .n8n-progress { background: #e2e8f0; height: 8px; border-radius: 4px; overflow: hidden; margin: 30px 0; }
    .n8n-progress-bar { background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); height: 100%; width: 60%; animation: loading 2s ease-in-out infinite; }
    @keyframes loading { 0%, 100% { transform: translateX(-100%); } 50% { transform: translateX(150%); } }
    .n8n-setup-list { text-align: left; display: inline-block; margin: 20px 0; }
    .n8n-setup-item { color: #4a5568; margin: 8px 0; }
    .n8n-setup-item i { color: #667eea; margin-right: 8px; }
    </style>';

    $output .= '<div class="n8n-provisioning">';
    $output .= '<div class="n8n-provisioning-card">';
    $output .= '<div class="n8n-spinner"></div>';
    $output .= '<h2 style="margin: 0 0 10px 0; color: #2d3748;">Setting Up Your n8n Instance</h2>';
    $output .= '<p style="color: #718096; margin-bottom: 20px;">Your workflow automation service is being provisioned. This typically takes 2-5 minutes.</p>';
    
    $output .= '<div class="n8n-progress">';
    $output .= '<div class="n8n-progress-bar"></div>';
    $output .= '</div>';
    
    $output .= '<div class="n8n-setup-list">';
    $output .= '<div class="n8n-setup-item"><i class="fa fa-check-circle"></i> Creating your n8n container</div>';
    $output .= '<div class="n8n-setup-item"><i class="fa fa-check-circle"></i> Setting up database</div>';
    $output .= '<div class="n8n-setup-item"><i class="fa fa-circle-o-notch fa-spin"></i> Configuring domain & SSL</div>';
    $output .= '<div class="n8n-setup-item"><i class="fa fa-circle-o"></i> Finalizing setup</div>';
    $output .= '</div>';
    
    $output .= '<p style="color: #a0aec0; font-size: 14px; margin-top: 30px;">This page will automatically update once your service is ready.</p>';
    $output .= '<button onclick="location.reload();" style="background: #e2e8f0; color: #4a5568; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: 500; margin-top: 10px;">Refresh Status</button>';
    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render main service dashboard when service is active
 */
function coolify_renderServiceDashboard($params, $serviceInfo)
{
    $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
    $statusClass = ($status == 'running') ? 'success' : (($status == 'stopped') ? 'danger' : 'warning');
    $statusText = ($status == 'running') ? 'Online' : (($status == 'stopped') ? 'Offline' : ucfirst($status));

    // Get the actual domain from Coolify service info
    $actualDomain = $params['domain']; // Default to WHMCS domain
    if (isset($serviceInfo['fqdn']) && !empty($serviceInfo['fqdn'])) {
        $actualDomain = $serviceInfo['fqdn'];

        // Update WHMCS domain if it's different
        if ($actualDomain !== $params['domain'] && $actualDomain !== 'Pending') {
            try {
                $pdo = Capsule::connection()->getPdo();
                $stmt = $pdo->prepare("UPDATE tblhosting SET domain = ? WHERE id = ?");
                $stmt->execute([$actualDomain, $params['serviceid']]);
            } catch (Exception $e) {
                // Log but don't fail
                logModuleCall('coolify', 'UpdateDomain', ['domain' => $actualDomain], $e->getMessage(), '');
            }
        }
    } elseif (isset($serviceInfo['domains']) && !empty($serviceInfo['domains'])) {
        $actualDomain = is_array($serviceInfo['domains']) ? $serviceInfo['domains'][0] : $serviceInfo['domains'];
    }

    // Add custom CSS for modern design
    $output = '<style>
    .n8n-dashboard { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
    .n8n-status-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 16px; padding: 30px; margin-bottom: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
    .n8n-status-badge { display: inline-block; background: rgba(255,255,255,0.2); padding: 5px 15px; border-radius: 20px; font-size: 14px; font-weight: 500; }
    .n8n-domain { font-size: 18px; opacity: 0.9; margin-top: 10px; }
    .n8n-action-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 25px; }
    .n8n-action-card { background: white; border-radius: 12px; padding: 25px; text-align: center; box-shadow: 0 4px 15px rgba(0,0,0,0.08); transition: transform 0.2s, box-shadow 0.2s; }
    .n8n-action-card:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.12); }
    .n8n-action-icon { font-size: 40px; margin-bottom: 15px; }
    .n8n-action-title { font-size: 18px; font-weight: 600; margin-bottom: 8px; color: #333; }
    .n8n-action-desc { color: #666; font-size: 14px; margin-bottom: 20px; }
    .n8n-btn { display: inline-block; padding: 10px 24px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: all 0.2s; }
    .n8n-btn-primary { background: #667eea; color: white; }
    .n8n-btn-primary:hover { background: #5a67d8; color: white; text-decoration: none; }
    .n8n-btn-secondary { background: #e2e8f0; color: #4a5568; }
    .n8n-btn-secondary:hover { background: #cbd5e0; color: #2d3748; text-decoration: none; }
    .n8n-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 25px; }
    .n8n-info-item { background: #f7fafc; border-radius: 8px; padding: 15px; text-align: center; }
    .n8n-info-label { color: #718096; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; }
    .n8n-info-value { font-size: 16px; font-weight: 600; color: #2d3748; margin-top: 5px; }
    .n8n-control-bar { display: flex; gap: 10px; flex-wrap: wrap; }
    .n8n-control-btn { padding: 8px 16px; border-radius: 6px; border: none; font-weight: 500; cursor: pointer; transition: all 0.2s; }
    .n8n-control-btn:hover { transform: translateY(-1px); }
    .n8n-control-btn-stop { background: #feb2b2; color: #742a2a; }
    .n8n-control-btn-stop:hover { background: #fc8181; }
    .n8n-control-btn-start { background: #9ae6b4; color: #22543d; }
    .n8n-control-btn-start:hover { background: #68d391; }
    .n8n-control-btn-restart { background: #90cdf4; color: #2c5282; }
    .n8n-control-btn-restart:hover { background: #63b3ed; }
    .n8n-control-btn-update { background: #667eea; color: white; text-decoration: none; }
    .n8n-control-btn-update:hover { background: #5a67d8; color: white; text-decoration: none; transform: translateY(-1px); }
    .n8n-version-update-btn { display: inline-block; padding: 4px 8px; background: #667eea; color: white; border-radius: 4px; font-size: 11px; text-decoration: none; font-weight: 500; transition: all 0.2s; }
    .n8n-version-update-btn:hover { background: #5a67d8; color: white; text-decoration: none; transform: translateY(-1px); }
    @media (max-width: 768px) {
        .n8n-action-grid { grid-template-columns: 1fr; }
        .n8n-info-grid { grid-template-columns: 1fr 1fr; }
    }
    </style>';

    $output .= '<div class="n8n-dashboard">';

    // Status Card
    $output .= '<div class="n8n-status-card">';
    $output .= '<div style="display: flex; justify-content: space-between; align-items: start; flex-wrap: wrap;">';
    $output .= '<div>';
    $output .= '<h2 style="margin: 0 0 10px 0; font-size: 28px; font-weight: 600;">n8n Workflow Automation</h2>';
    $output .= '<span class="n8n-status-badge"><i class="fa fa-circle" style="color: ' . ($status == 'running' ? '#68d391' : '#fc8181') . ';"></i> ' . $statusText . '</span>';
    
    if ($actualDomain && $actualDomain !== 'Pending') {
        $normalizedUrl = coolify_normalizeUrl($actualDomain);
        $output .= '<div class="n8n-domain"><i class="fa fa-globe"></i> ' . htmlspecialchars($actualDomain) . '</div>';
    }
    $output .= '</div>';
    
    if ($status == 'running' && $actualDomain && $actualDomain !== 'Pending') {
        $normalizedUrl = coolify_normalizeUrl($actualDomain);
        $output .= '<div style="margin-top: 10px;">';
        $output .= '<a href="' . htmlspecialchars($normalizedUrl) . '" target="_blank" class="n8n-btn n8n-btn-primary" style="background: rgba(255,255,255,0.9); color: #667eea;">';
        $output .= '<i class="fa fa-rocket"></i> Open n8n';
        $output .= '</a>';
        $output .= '</div>';
    }
    $output .= '</div>';
    $output .= '</div>';

    // Quick Actions Grid
    if ($status == 'running' && $actualDomain && $actualDomain !== 'Pending') {
        $normalizedUrl = coolify_normalizeUrl($actualDomain);
        $output .= '<div class="n8n-action-grid">';
        
        // Workflows Card
        $output .= '<div class="n8n-action-card">';
        $output .= '<div class="n8n-action-icon" style="color: #667eea;"><i class="fa fa-sitemap"></i></div>';
        $output .= '<div class="n8n-action-title">Workflows</div>';
        $output .= '<div class="n8n-action-desc">Create and manage your automation workflows</div>';
        $output .= '<a href="' . htmlspecialchars($normalizedUrl) . '/workflows" target="_blank" class="n8n-btn n8n-btn-primary">View Workflows</a>';
        $output .= '</div>';
        
        // Executions Card
        $output .= '<div class="n8n-action-card">';
        $output .= '<div class="n8n-action-icon" style="color: #48bb78;"><i class="fa fa-history"></i></div>';
        $output .= '<div class="n8n-action-title">Executions</div>';
        $output .= '<div class="n8n-action-desc">Monitor your workflow runs and debug issues</div>';
        $output .= '<a href="' . htmlspecialchars($normalizedUrl) . '/executions" target="_blank" class="n8n-btn n8n-btn-primary">View History</a>';
        $output .= '</div>';
        
        // Templates Card
        $output .= '<div class="n8n-action-card">';
        $output .= '<div class="n8n-action-icon" style="color: #ed8936;"><i class="fa fa-magic"></i></div>';
        $output .= '<div class="n8n-action-title">Templates</div>';
        $output .= '<div class="n8n-action-desc">Start with pre-built workflow templates</div>';
        $output .= '<a href="https://n8n.io/workflows" target="_blank" class="n8n-btn n8n-btn-secondary">Browse Templates</a>';
        $output .= '</div>';
        
        $output .= '</div>';
    }

    // Service Info Grid
    $output .= '<div class="n8n-info-grid">';
    
    // Version Info
    $currentVersion = 'Unknown';
    if ($serviceInfo && isset($serviceInfo['docker_compose_raw'])) {
        $dockerCompose = base64_decode($serviceInfo['docker_compose_raw']);
        if (preg_match('/image:\s*docker\.n8n\.io\/n8nio\/n8n:?([^\s]*)/', $dockerCompose, $matches)) {
            $currentVersion = !empty($matches[1]) ? $matches[1] : 'latest';
        }
    }
    
    $output .= '<div class="n8n-info-item">';
    $output .= '<div class="n8n-info-label">Version</div>';
    $output .= '<div class="n8n-info-value">' . htmlspecialchars($currentVersion) . '</div>';
    $output .= '<div style="margin-top: 8px;">';
    $output .= '<a href="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '&modop=custom&a=updateN8nVersionClient" class="n8n-version-update-btn">';
    $output .= '<i class="fa fa-arrow-circle-up"></i> Update';
    $output .= '</a>';
    $output .= '</div>';
    $output .= '</div>';
    
    $output .= '<div class="n8n-info-item">';
    $output .= '<div class="n8n-info-label">Security</div>';
    $output .= '<div class="n8n-info-value"><i class="fa fa-lock" style="color: #48bb78;"></i> SSL</div>';
    $output .= '</div>';
    
    $output .= '<div class="n8n-info-item">';
    $output .= '<div class="n8n-info-label">Database</div>';
    $output .= '<div class="n8n-info-value">SQLite</div>';
    $output .= '</div>';
    
    $output .= '<div class="n8n-info-item">';
    $output .= '<div class="n8n-info-label">Support</div>';
    $output .= '<div class="n8n-info-value">24/7</div>';
    $output .= '</div>';
    
    $output .= '</div>';

    // Service Controls
    $output .= '<div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.08);">';
    $output .= '<h4 style="margin: 0 0 15px 0; color: #2d3748;"><i class="fa fa-cogs"></i> Service Controls</h4>';
    $output .= '<div class="n8n-control-bar">';
    
    if ($status == 'running') {
        $output .= '<form method="post" style="display: inline-block; margin-right: 10px;">';
        $output .= '<input type="hidden" name="modop" value="custom">';
        $output .= '<input type="hidden" name="a" value="stopService">';
        $output .= '<button type="submit" class="n8n-control-btn n8n-control-btn-stop" onclick="return confirm(\'Stop your n8n service?\');">';
        $output .= '<i class="fa fa-stop"></i> Stop';
        $output .= '</button>';
        $output .= '</form>';
        
        $output .= '<form method="post" style="display: inline-block; margin-right: 10px;">';
        $output .= '<input type="hidden" name="modop" value="custom">';
        $output .= '<input type="hidden" name="a" value="restartService">';
        $output .= '<button type="submit" class="n8n-control-btn n8n-control-btn-restart" onclick="return confirm(\'Restart your n8n service?\');">';
        $output .= '<i class="fa fa-refresh"></i> Restart';
        $output .= '</button>';
        $output .= '</form>';
    } elseif ($status == 'stopped') {
        $output .= '<form method="post" style="display: inline-block; margin-right: 10px;">';
        $output .= '<input type="hidden" name="modop" value="custom">';
        $output .= '<input type="hidden" name="a" value="startService">';
        $output .= '<button type="submit" class="n8n-control-btn n8n-control-btn-start">';
        $output .= '<i class="fa fa-play"></i> Start Service';
        $output .= '</button>';
        $output .= '</form>';
    }
    
    // Add Update Version button (always available)
    $output .= '<a href="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '&modop=custom&a=updateN8nVersionClient" class="n8n-control-btn n8n-control-btn-update" style="margin-right: 10px;">';
    $output .= '<i class="fa fa-arrow-circle-up"></i> Update Version';
    $output .= '</a>';
    
    $output .= '<button onclick="location.reload();" class="n8n-control-btn" style="background: #e2e8f0; color: #4a5568;">';
    $output .= '<i class="fa fa-sync"></i> Refresh';
    $output .= '</button>';
    
    $output .= '</div>';
    $output .= '</div>';

    // Quick Links Footer
    $output .= '<div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">';
    $output .= '<a href="https://docs.n8n.io" target="_blank" style="color: #718096; margin: 0 15px; font-size: 14px;"><i class="fa fa-book"></i> Docs</a>';
    $output .= '<a href="https://community.n8n.io" target="_blank" style="color: #718096; margin: 0 15px; font-size: 14px;"><i class="fa fa-users"></i> Community</a>';
    $output .= '<a href="submitticket.php" style="color: #718096; margin: 0 15px; font-size: 14px;"><i class="fa fa-life-ring"></i> Support</a>';
    $output .= '</div>';

    $output .= '</div>';

    // Add loading state script
    $output .= '<script>
    document.addEventListener("DOMContentLoaded", function() {
        var forms = document.querySelectorAll("form[method=post]");
        forms.forEach(function(form) {
            form.addEventListener("submit", function(e) {
                var button = form.querySelector("button[type=submit]");
                if (button) {
                    button.disabled = true;
                    var originalContent = button.innerHTML;
                    button.innerHTML = "<i class=\"fa fa-spinner fa-spin\"></i> Processing...";
                    setTimeout(function() {
                        button.disabled = false;
                        button.innerHTML = originalContent;
                    }, 5000);
                }
            });
        });
    });
    </script>';

    return $output;
}

/**
 * Render service unavailable template
 */
function coolify_renderServiceUnavailable($params)
{
    $normalizedUrl = coolify_normalizeUrl($params['domain']);
    
    $output = '<style>
    .n8n-unavailable { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; text-align: center; padding: 60px 20px; }
    .n8n-unavailable-card { background: white; border-radius: 16px; padding: 40px; max-width: 600px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
    .n8n-status-icon { font-size: 60px; color: #f6ad55; margin-bottom: 20px; }
    .n8n-btn-group { display: flex; gap: 10px; justify-content: center; margin-top: 30px; flex-wrap: wrap; }
    .n8n-btn { padding: 12px 24px; border-radius: 8px; border: none; font-weight: 500; cursor: pointer; text-decoration: none; transition: all 0.2s; }
    .n8n-btn-primary { background: #667eea; color: white; }
    .n8n-btn-primary:hover { background: #5a67d8; color: white; text-decoration: none; }
    .n8n-btn-secondary { background: #e2e8f0; color: #4a5568; }
    .n8n-btn-secondary:hover { background: #cbd5e0; color: #2d3748; text-decoration: none; }
    </style>';

    $output .= '<div class="n8n-unavailable">';
    $output .= '<div class="n8n-unavailable-card">';
    $output .= '<div class="n8n-status-icon"><i class="fa fa-exclamation-circle"></i></div>';
    $output .= '<h2 style="margin: 0 0 10px 0; color: #2d3748;">Service Status Temporarily Unavailable</h2>';
    $output .= '<p style="color: #718096; margin-bottom: 20px;">We cannot retrieve your service status right now. Your n8n instance may still be starting up or there\'s a temporary connection issue.</p>';
    
    $output .= '<div class="n8n-btn-group">';
    $output .= '<a href="' . htmlspecialchars($normalizedUrl) . '" target="_blank" class="n8n-btn n8n-btn-primary">';
    $output .= '<i class="fa fa-external-link"></i> Try Accessing n8n';
    $output .= '</a>';
    $output .= '<button onclick="location.reload();" class="n8n-btn n8n-btn-secondary">';
    $output .= '<i class="fa fa-refresh"></i> Refresh Status';
    $output .= '</button>';
    $output .= '</div>';
    
    $output .= '<p style="color: #a0aec0; font-size: 14px; margin-top: 30px;">If the issue persists, please <a href="submitticket.php" style="color: #667eea;">contact support</a>.</p>';
    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render service error template
 */
function coolify_renderServiceError($params, $exception)
{
    $normalizedUrl = coolify_normalizeUrl($params['domain']);
    
    $output = '<style>
    .n8n-error { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; text-align: center; padding: 60px 20px; }
    .n8n-error-card { background: white; border-radius: 16px; padding: 40px; max-width: 600px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
    .n8n-status-icon { font-size: 60px; color: #4299e1; margin-bottom: 20px; }
    .n8n-btn { display: inline-block; padding: 12px 24px; border-radius: 8px; background: #667eea; color: white; font-weight: 500; cursor: pointer; text-decoration: none; transition: all 0.2s; }
    .n8n-btn:hover { background: #5a67d8; color: white; text-decoration: none; }
    </style>';

    $output .= '<div class="n8n-error">';
    $output .= '<div class="n8n-error-card">';
    $output .= '<div class="n8n-status-icon"><i class="fa fa-info-circle"></i></div>';
    $output .= '<h2 style="margin: 0 0 10px 0; color: #2d3748;">Your n8n Service is Ready</h2>';
    $output .= '<p style="color: #718096; margin-bottom: 30px;">We cannot check the current status, but your service is provisioned and should be accessible.</p>';
    
    $output .= '<a href="' . htmlspecialchars($normalizedUrl) . '" target="_blank" class="n8n-btn">';
    $output .= '<i class="fa fa-external-link"></i> Access n8n Dashboard';
    $output .= '</a>';
    
    $output .= '<p style="color: #a0aec0; font-size: 14px; margin-top: 30px;">Need help? <a href="submitticket.php" style="color: #667eea;">Contact our support team</a>.</p>';
    $output .= '</div>';
    $output .= '</div>';

    return $output;
}

/**
 * Render client-friendly n8n version update form
 */
function coolify_renderClientUpdateVersionForm($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">Service configuration error. Please contact support.</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return '<div class="alert alert-danger">Service not found</div>';
        }

        // Get current service info
        $serviceInfo = $api->getService($serviceUuid);
        $currentVersion = 'Unknown';
        
        // Try to extract current version from docker compose
        if (isset($serviceInfo['docker_compose_raw'])) {
            $dockerCompose = base64_decode($serviceInfo['docker_compose_raw']);
            if (preg_match('/image:\s*docker\.n8n\.io\/n8nio\/n8n:?([^\s]*)/', $dockerCompose, $matches)) {
                $currentVersion = !empty($matches[1]) ? $matches[1] : 'latest';
            }
        }

        $output = '<style>
        .n8n-update { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .n8n-update-card { background: white; border-radius: 16px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .n8n-version-badge { display: inline-block; background: #e6f7ff; color: #1890ff; padding: 8px 16px; border-radius: 20px; font-weight: 600; font-size: 16px; }
        .n8n-form-group { margin: 25px 0; }
        .n8n-form-label { display: block; color: #2d3748; font-weight: 600; margin-bottom: 8px; }
        .n8n-form-select { width: 100%; padding: 12px 16px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 16px; transition: border-color 0.2s; }
        .n8n-form-select:focus { outline: none; border-color: #667eea; }
        .n8n-warning-box { background: #fef5e7; border: 1px solid #f6ad55; border-radius: 12px; padding: 20px; margin: 25px 0; }
        .n8n-warning-title { color: #d97706; font-weight: 600; margin-bottom: 10px; }
        .n8n-warning-list { color: #92400e; margin: 0; padding-left: 20px; }
        .n8n-warning-list li { margin: 5px 0; }
        .n8n-btn { padding: 14px 28px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; font-size: 16px; transition: all 0.2s; text-decoration: none; display: inline-block; }
        .n8n-btn-primary { background: #667eea; color: white; }
        .n8n-btn-primary:hover { background: #5a67d8; color: white; text-decoration: none; transform: translateY(-1px); }
        .n8n-btn-secondary { background: #e2e8f0; color: #4a5568; margin-left: 10px; }
        .n8n-btn-secondary:hover { background: #cbd5e0; color: #2d3748; text-decoration: none; }
        .n8n-back-link { color: #718096; font-size: 14px; display: inline-block; margin-bottom: 20px; text-decoration: none; }
        .n8n-back-link:hover { color: #667eea; text-decoration: none; }
        </style>';
        
        $output .= '<div class="n8n-update">';
        $output .= '<a href="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '" class="n8n-back-link"><i class="fa fa-arrow-left"></i> Back to Service</a>';
        
        $output .= '<div class="n8n-update-card">';
        $output .= '<h2 style="margin: 0 0 20px 0; color: #2d3748;"><i class="fa fa-refresh" style="color: #667eea;"></i> Update n8n Version</h2>';
        $output .= '<p style="color: #718096; margin-bottom: 20px;">Current version: <span class="n8n-version-badge">' . htmlspecialchars($currentVersion) . '</span></p>';
        
        $output .= '<form method="post" action="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '&modop=custom&a=updateN8nVersionClient">';
        
        $output .= '<div class="n8n-form-group">';
        $output .= '<label class="n8n-form-label" for="n8n_version">Select New Version</label>';
        $output .= '<select name="n8n_version" id="n8n_version" class="n8n-form-select">';
        $output .= '<option value="latest">Latest (Recommended)</option>';
        
        // Fetch available n8n versions - show only recent stable versions for customers
        $availableVersions = coolify_fetchN8nVersions(10);
        foreach ($availableVersions as $version) {
            $selected = ($currentVersion === $version) ? ' selected' : '';
            $isCurrent = ($currentVersion === $version) ? ' (Current)' : '';
            $output .= '<option value="' . htmlspecialchars($version) . '"' . $selected . '>' . htmlspecialchars($version) . $isCurrent . '</option>';
        }
        
        $output .= '</select>';
        $output .= '</div>';
        
        $output .= '<div class="n8n-warning-box">';
        $output .= '<div class="n8n-warning-title"><i class="fa fa-exclamation-triangle"></i> Before You Update</div>';
        $output .= '<ul class="n8n-warning-list">';
        $output .= '<li>Export your workflows as backup</li>';
        $output .= '<li>Service will restart (1-2 minutes downtime)</li>';
        $output .= '<li>Check <a href="https://github.com/n8n-io/n8n/releases" target="_blank" style="color: #d97706;">release notes</a> for breaking changes</li>';
        $output .= '</ul>';
        $output .= '</div>';
        
        $output .= '<div style="margin-top: 30px;">';
        $output .= '<button type="submit" class="n8n-btn n8n-btn-primary" onclick="return confirm(\'Update n8n? Your service will restart automatically.\');">';
        $output .= '<i class="fa fa-refresh"></i> Update n8n';
        $output .= '</button>';
        $output .= '<a href="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '" class="n8n-btn n8n-btn-secondary">Cancel</a>';
        $output .= '</div>';
        $output .= '</form>';
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'renderClientUpdateVersionForm',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return '<div class="alert alert-danger">Unable to load version update form. Please try again later or contact support.</div>';
    }
}

/**
 * Normalize URL to ensure proper protocol and format
 * Fixes issues with double protocols like https://http//domain
 */
function coolify_normalizeUrl($url)
{
    if (empty($url)) {
        return '';
    }

    // Remove any existing protocol
    $url = preg_replace('/^https?:\/\//', '', $url);

    // Remove any remaining protocol fragments
    $url = preg_replace('/^http\/\//', '', $url);

    // Ensure we have a clean domain
    $url = trim($url, '/');

    // Add https:// protocol
    return 'https://' . $url;
}

/**
 * Get server configuration from WHMCS database
 * This allows multiple Coolify instances to be configured as different servers
 */
function coolify_getServerConfiguration($serverId)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get server details from WHMCS
        $stmt = $pdo->prepare("SELECT * FROM tblservers WHERE id = ?");
        $stmt->execute([$serverId]);
        $server = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$server) {
            return false;
        }

        // Use WHMCS server fields for configuration
        $config = [];

        // Primary: Use Status Address field for Coolify API base URL
        if (!empty($server['statusaddress'])) {
            $config['coolify_url'] = $server['statusaddress'];
        }

        // Fallback: Use IP Address field if status address is empty
        if (empty($config['coolify_url']) && !empty($server['ipaddress'])) {
            $config['coolify_url'] = $server['ipaddress'];
        }

        // Final fallback: Use hostname if both above are empty
        if (empty($config['coolify_url']) && !empty($server['hostname'])) {
            $config['coolify_url'] = $server['hostname'];
        }

        // Try to get additional config from server name (JSON format)
        if (!empty($server['name'])) {
            // Check if server name contains JSON configuration
            $nameConfig = json_decode($server['name'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($nameConfig)) {
                $config = array_merge($config, $nameConfig);
            }
        }

        // Try to get config from server notes/comments field
        if (!empty($server['nameserver1'])) {
            // Use nameserver1 field for project_uuid
            $config['project_uuid'] = $server['nameserver1'];
        }

        if (!empty($server['nameserver2'])) {
            // Use nameserver2 field for server_uuid
            $config['server_uuid'] = $server['nameserver2'];
        }

        if (!empty($server['nameserver3'])) {
            // Use nameserver3 field for destination_uuid
            $config['destination_uuid'] = $server['nameserver3'];
        }

        if (!empty($server['nameserver4'])) {
            // Use nameserver4 field for environment_name
            $config['environment_name'] = $server['nameserver4'];
        }

        if (!empty($server['nameserver5'])) {
            // Use nameserver5 field for base_domain
            $config['base_domain'] = $server['nameserver5'];
        }

        // Set defaults for any missing values
        $config = array_merge([
            'project_uuid' => '',
            'environment_name' => 'production',
            'server_uuid' => '',
            'destination_uuid' => '',
            'coolify_url' => 'https://app.coolify.io',
            'base_domain' => 'yourdomain.com'
        ], $config);

        return $config;

    } catch (Exception $e) {
        logModuleCall('coolify', 'getServerConfiguration', ['server_id' => $serverId], $e->getMessage(), '');
        return false;
    }
}

/**
 * Send welcome email to customer when n8n service is provisioned
 */
function coolify_sendWelcomeEmail($params, $serviceInfo = null)
{
    try {
        // Generate email content
        $emailData = coolify_generateWelcomeEmail($params, $serviceInfo);

        // Get customer email
        $customerEmail = $params['clientsdetails']['email'];
        $customerName = $params['clientsdetails']['firstname'] . ' ' . $params['clientsdetails']['lastname'];

        // Prepare email parameters for WHMCS
        $emailParams = [
            'messagename' => 'n8n_welcome_email',
            'id' => $params['serviceid'],
            'customtype' => 'general',
            'customsubject' => $emailData['subject'],
            'custommessage' => $emailData['html'],
            'customvars' => [
                'client_name' => $customerName,
                'service_domain' => $params['domain'],
                'service_id' => $params['serviceid'],
                'product_name' => $params['productname']
            ]
        ];

        // Use WHMCS built-in email system
        if (function_exists('sendMessage')) {
            // Try using WHMCS sendMessage function
            $result = sendMessage('General', $params['serviceid'], $emailParams);
        } else {
            // Fallback to direct email sending
            $result = coolify_sendDirectEmail($customerEmail, $emailData['subject'], $emailData['html'], $emailData['text']);
        }

        // Log successful email sending
        logModuleCall('coolify', 'WelcomeEmail_Sent', [
            'customer_email' => $customerEmail,
            'service_id' => $params['serviceid'],
            'subject' => $emailData['subject']
        ], $result, '');

        return $result;

    } catch (Exception $e) {
        logModuleCall('coolify', 'WelcomeEmail_Error', $params, $e->getMessage(), $e->getTraceAsString());
        throw $e;
    }
}

/**
 * Send email directly using PHP mail function as fallback
 */
function coolify_sendDirectEmail($to, $subject, $htmlBody, $textBody)
{
    // Set headers for HTML email
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "From: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "Reply-To: support@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "X-Mailer: WHMCS Coolify Module\r\n";

    // Send the email
    $result = mail($to, $subject, $htmlBody, $headers);

    return $result;
}

/**
 * Ensure custom field exists for a product
 */
function ensureCustomFieldExists($productId, $fieldName, $description)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Check if custom field already exists
        $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = ?");
        $stmt->execute([$productId, $fieldName]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            return $existing['id'];
        }

        // Create the custom field
        $stmt = $pdo->prepare("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, adminonly, required, showorder, showinvoice) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['product', $productId, $fieldName, 'text', $description, '1', '0', '0', '0']);

        return $pdo->lastInsertId();

    } catch (Exception $e) {
        logModuleCall('coolify', 'ensureCustomFieldExists', compact('productId', 'fieldName', 'description'), $e->getMessage(), '');
        return false;
    }
}

/**
 * Helper function to fetch latest n8n versions from GitHub releases.
 */
function coolify_fetchN8nVersions($limit = 15)
{
    try {
        $url = 'https://api.github.com/repos/n8n-io/n8n/releases?per_page=' . $limit;
        
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_HTTPHEADER => array(
                'Accept: application/vnd.github.v3+json',
                'User-Agent: WHMCS-Coolify-Module'
            )
        ));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200 || !$response) {
            // Return default versions if API fails
            return coolify_getDefaultN8nVersions();
        }
        
        $releases = json_decode($response, true);
        $versions = array();
        
        foreach ($releases as $release) {
            if (!isset($release['prerelease']) || $release['prerelease']) {
                continue; // Skip pre-releases
            }
            
            if (isset($release['tag_name'])) {
                // Remove 'v' prefix if present
                $version = ltrim($release['tag_name'], 'v');
                $versions[] = $version;
            }
        }
        
        return !empty($versions) ? $versions : coolify_getDefaultN8nVersions();
        
    } catch (Exception $e) {
        // Return default versions if something goes wrong
        return coolify_getDefaultN8nVersions();
    }
}

/**
 * Get default n8n versions as fallback.
 */
function coolify_getDefaultN8nVersions()
{
    return array(
        '1.19.0',
        '1.18.0',
        '1.17.0',
        '1.16.0',
        '1.15.0',
        '1.14.0',
        '1.13.0',
        '1.12.0',
        '1.11.0',
        '1.10.0',
        '1.9.0',
        '1.8.0',
        '1.7.0',
        '1.6.0',
        '1.5.0'
    );
}

/**
 * Render custom modules management form for client area
 */
function coolify_renderCustomModulesForm($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured. Please contact support.</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return '<div class="alert alert-danger">Service not found</div>';
        }

        // Get current custom modules
        $currentModules = coolify_getCurrentCustomModules($params['serviceid']);

        $output = '<style>
        .n8n-modules { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .n8n-modules-card { background: white; border-radius: 16px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .n8n-form-group { margin: 25px 0; }
        .n8n-form-label { display: block; color: #2d3748; font-weight: 600; margin-bottom: 8px; }
        .n8n-form-input { width: 100%; padding: 12px 16px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 16px; transition: border-color 0.2s; }
        .n8n-form-input:focus { outline: none; border-color: #667eea; }
        .n8n-help-text { color: #718096; font-size: 14px; margin-top: 5px; }
        .n8n-warning-box { background: #fef5e7; border: 1px solid #f6ad55; border-radius: 12px; padding: 20px; margin: 25px 0; }
        .n8n-warning-title { color: #d97706; font-weight: 600; margin-bottom: 10px; }
        .n8n-warning-list { color: #92400e; margin: 0; padding-left: 20px; }
        .n8n-warning-list li { margin: 5px 0; }
        .n8n-btn { padding: 14px 28px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; font-size: 16px; transition: all 0.2s; text-decoration: none; display: inline-block; }
        .n8n-btn-primary { background: #667eea; color: white; }
        .n8n-btn-primary:hover { background: #5a67d8; color: white; text-decoration: none; transform: translateY(-1px); }
        .n8n-btn-secondary { background: #e2e8f0; color: #4a5568; margin-left: 10px; }
        .n8n-btn-secondary:hover { background: #cbd5e0; color: #2d3748; text-decoration: none; }
        .n8n-btn-danger { background: #fc8181; color: white; padding: 8px 16px; font-size: 14px; }
        .n8n-btn-danger:hover { background: #f56565; color: white; text-decoration: none; transform: translateY(-1px); }
        .n8n-back-link { color: #718096; font-size: 14px; display: inline-block; margin-bottom: 20px; text-decoration: none; }
        .n8n-back-link:hover { color: #667eea; text-decoration: none; }
        .n8n-module-item { background: #f7fafc; border-radius: 8px; padding: 15px; margin: 10px 0; display: flex; justify-content: space-between; align-items: center; }
        .n8n-module-name { font-weight: 600; color: #2d3748; }
        .n8n-info-box { background: #e6fffa; border: 1px solid #38b2ac; border-radius: 12px; padding: 20px; margin: 25px 0; }
        .n8n-info-title { color: #2c7a7b; font-weight: 600; margin-bottom: 10px; }
        .n8n-info-list { color: #285e61; margin: 0; padding-left: 20px; }
        .n8n-info-list li { margin: 5px 0; }
        </style>';

        $output .= '<div class="n8n-modules">';
        $output .= '<a href="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '" class="n8n-back-link"><i class="fa fa-arrow-left"></i> Back to Service</a>';

        $output .= '<div class="n8n-modules-card">';
        $output .= '<h2 style="margin: 0 0 20px 0; color: #2d3748;"><i class="fa fa-puzzle-piece" style="color: #667eea;"></i> Manage Custom n8n Modules</h2>';
        $output .= '<p style="color: #718096; margin-bottom: 20px;">Install community modules to extend n8n functionality with additional nodes and integrations.</p>';

        // Show currently installed modules
        if (!empty($currentModules)) {
            $output .= '<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Installed Modules</h3>';
            foreach ($currentModules as $module) {
                $output .= '<div class="n8n-module-item">';
                $output .= '<span class="n8n-module-name">' . htmlspecialchars($module) . '</span>';
                $output .= '<form method="post" action="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '&modop=custom&a=manageCustomModules" style="margin: 0;">';
                $output .= '<input type="hidden" name="action" value="uninstall_module">';
                $output .= '<input type="hidden" name="module_name" value="' . htmlspecialchars($module) . '">';
                $output .= '<button type="submit" class="n8n-btn n8n-btn-danger" onclick="return confirm(\'Uninstall ' . htmlspecialchars($module) . '? This will restart your service.\');">';
                $output .= '<i class="fa fa-trash"></i> Uninstall';
                $output .= '</button>';
                $output .= '</form>';
                $output .= '</div>';
            }
        } else {
            $output .= '<p style="color: #a0aec0; font-style: italic; margin: 20px 0;">No custom modules installed yet.</p>';
        }

        // Install new module form
        $output .= '<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Install New Module</h3>';
        $output .= '<form method="post" action="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '&modop=custom&a=manageCustomModules">';
        $output .= '<input type="hidden" name="action" value="install_module">';

        $output .= '<div class="n8n-form-group">';
        $output .= '<label class="n8n-form-label" for="module_name">Module Name</label>';
        $output .= '<input type="text" name="module_name" id="module_name" class="n8n-form-input" placeholder="e.g., n8n-nodes-telegram" required>';
        $output .= '<div class="n8n-help-text">Enter the npm package name of the n8n community module you want to install.</div>';
        $output .= '</div>';

        $output .= '<div class="n8n-warning-box">';
        $output .= '<div class="n8n-warning-title"><i class="fa fa-exclamation-triangle"></i> Important Notes</div>';
        $output .= '<ul class="n8n-warning-list">';
        $output .= '<li>Installing modules will restart your n8n service (1-2 minutes downtime)</li>';
        $output .= '<li>Only install modules from trusted sources</li>';
        $output .= '<li>Some modules may require additional configuration</li>';
        $output .= '<li>Module compatibility depends on your n8n version</li>';
        $output .= '</ul>';
        $output .= '</div>';

        $output .= '<div style="margin-top: 30px;">';
        $output .= '<button type="submit" class="n8n-btn n8n-btn-primary" onclick="return confirm(\'Install this module? Your service will restart automatically.\');">';
        $output .= '<i class="fa fa-plus"></i> Install Module';
        $output .= '</button>';
        $output .= '<a href="clientarea.php?action=productdetails&id=' . $params['serviceid'] . '" class="n8n-btn n8n-btn-secondary">Cancel</a>';
        $output .= '</div>';
        $output .= '</form>';

        // Information section
        $output .= '<div class="n8n-info-box">';
        $output .= '<div class="n8n-info-title"><i class="fa fa-info-circle"></i> Popular Community Modules</div>';
        $output .= '<ul class="n8n-info-list">';
        $output .= '<li><strong>n8n-nodes-telegram</strong> - Telegram Bot integration</li>';
        $output .= '<li><strong>n8n-nodes-discord</strong> - Discord Bot integration</li>';
        $output .= '<li><strong>n8n-nodes-whatsapp</strong> - WhatsApp Business API</li>';
        $output .= '<li><strong>n8n-nodes-mysql</strong> - Enhanced MySQL operations</li>';
        $output .= '<li><strong>n8n-nodes-redis</strong> - Redis database operations</li>';
        $output .= '</ul>';
        $output .= '<p style="margin-top: 15px; color: #285e61;">Find more modules at <a href="https://www.npmjs.com/search?q=n8n-nodes" target="_blank" style="color: #2c7a7b;">npmjs.com</a></p>';
        $output .= '</div>';

        $output .= '</div>';
        $output .= '</div>';

        return $output;

    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'renderCustomModulesForm',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );

        return '<div class="alert alert-danger">Unable to load custom modules form. Please try again later or contact support.</div>';
    }
}

