# WHMCS Coolify n8n Module - Service Management Templates

## Overview

This WHMCS module provides comprehensive service management templates for Coolify-deployed n8n instances. It includes enhanced client and admin interfaces with modern, user-friendly templates for managing workflow automation services.

## Features

### 🎨 Enhanced Client Area Templates
- **Service Dashboard**: Comprehensive overview with status, quick actions, and service information
- **Provisioning Template**: Professional loading screen during service setup
- **User Guide**: Complete documentation and getting started guide
- **Status Monitoring**: Real-time service health and performance metrics
- **Error Handling**: Graceful error states with helpful information

### 🛠️ Advanced Admin Templates
- **Service Overview**: Detailed admin dashboard with service management tools
- **Enhanced Logs**: Color-coded log viewer with copy functionality
- **Server Resources**: Real-time server resource monitoring
- **Quick Actions**: One-click service management operations

### 📚 Documentation Templates
- **Getting Started Guide**: Step-by-step workflow creation tutorial
- **Use Case Examples**: Popular automation scenarios
- **Integration Library**: 500+ available service integrations
- **Troubleshooting**: Common issues and solutions

## Template Structure

```
modules/servers/coolify/
├── coolify.php                 # Main module file with enhanced client area
├── lib/
│   ├── CoolifyAPI.php         # API wrapper for Coolify
│   └── AdminFunctions.php     # Enhanced admin functions
├── templates/
│   ├── AdminTemplates.php     # Admin interface templates
│   ├── UserGuideTemplate.php  # User documentation templates
│   └── StatusTemplate.php     # Status monitoring templates
└── hooks.php                  # Module hooks and setup
```

## Client Area Features

### Service Dashboard
- **Real-time Status**: Live service status with visual indicators
- **Quick Access**: Direct links to n8n dashboard and workflows
- **Service Information**: Creation date, domain, database details
- **Getting Started**: Interactive tutorial for new users

### User Guide Integration
- **Quick Start**: 5-step workflow creation process
- **Popular Use Cases**: Email automation, data sync, social media
- **Available Integrations**: Google Workspace, Slack, Shopify, and 500+ more
- **Example Workflows**: Pre-built automation examples
- **Best Practices**: Tips for effective workflow design

### Status Monitoring
- **Health Indicators**: Service, database, and SSL status
- **Performance Metrics**: CPU, memory, disk, and network usage
- **Uptime Tracking**: 30-day availability statistics
- **Alert System**: Proactive issue notifications

## Admin Area Features

### Enhanced Service Management
- **Comprehensive Overview**: Service details, customer info, and access links
- **Resource Monitoring**: Real-time server resource usage
- **Environment Variables**: Secure display of service configuration
- **Quick Actions**: Restart, logs, configuration updates

### Advanced Log Viewer
- **Color-coded Logs**: Error, warning, and info level highlighting
- **Copy Functionality**: One-click log copying to clipboard
- **Auto-refresh**: Automatic log updates every 5 minutes
- **Enhanced Formatting**: Improved readability with timestamps

### Configuration Management
- **Memory Limits**: Adjustable container memory allocation
- **Environment Variables**: Secure environment variable management
- **Service Updates**: Real-time configuration updates

### Custom n8n Modules 🧩
- **Client Self-Service**: Clients can install/uninstall community modules
- **Admin Control**: Full administrative oversight of custom modules
- **Default Modules**: Pre-install modules for new services
- **Module Persistence**: Modules preserved during n8n version updates
- **Popular Modules**: Telegram, Discord, WhatsApp, MySQL, Redis integrations

## Installation & Setup

1. **Upload Module**: Place files in `modules/servers/coolify/`
2. **Configure Server**: Set up Coolify API credentials in WHMCS
3. **Create Product**: Configure n8n product with module settings
4. **Test Connection**: Verify API connectivity

### Required Configuration

#### Server-Level Configuration (Per Coolify Instance)
- **API Token**: Coolify Bearer token (stored in server Access Hash)
- **Project UUID**: Target Coolify project
- **Environment Name**: Deployment environment (e.g., production)
- **Server UUID**: Deployment server
- **Destination UUID**: Container destination
- **Coolify URL**: Your Coolify instance URL
- **Base Domain**: Domain for customer services

#### Product-Level Configuration (Per Service Plan)
- **Memory Limit**: Container memory allocation
- **Timezone**: Default timezone for instances
- **Docker Image**: n8n image version
- **Allow Custom Modules**: Enable/disable custom module installation
- **Default Custom Modules**: Pre-install specific modules for new services

## Template Customization

### Client Area Customization
```php
// Modify client dashboard in coolify.php
function coolify_ClientArea($params) {
    // Custom template logic here
    return coolify_renderServiceDashboard($params, $serviceInfo);
}
```

### Admin Interface Customization
```php
// Enhance admin templates in AdminTemplates.php
function coolify_renderAdminServiceOverview($params, $serviceInfo) {
    // Custom admin interface logic
}
```

### Status Monitoring Customization
```php
// Customize status templates in StatusTemplate.php
function coolify_renderStatusMonitoring($params, $serviceInfo) {
    // Custom monitoring logic
}
```

## API Integration

### Service Management
- **Create Service**: Automated n8n deployment with PostgreSQL
- **Status Monitoring**: Real-time service health checks
- **Log Retrieval**: Container log access and display
- **Resource Management**: Server resource monitoring

### Error Handling
- **Graceful Degradation**: Fallback templates for API failures
- **User-friendly Messages**: Clear error communication
- **Logging**: Comprehensive error logging for debugging

## Security Features

### Data Protection
- **Credential Masking**: Automatic password/token hiding
- **Secure API Calls**: Bearer token authentication
- **Input Validation**: XSS and injection protection
- **Access Control**: Admin-only sensitive operations

### Service Security
- **SSL Certificates**: Automatic Let's Encrypt integration
- **Database Security**: PostgreSQL with secure credentials
- **Network Isolation**: Container-level security
- **Backup Protection**: Automated data backup

## Performance Optimization

### Template Efficiency
- **Lazy Loading**: On-demand template rendering
- **Caching**: Service information caching
- **Minimal API Calls**: Optimized API usage
- **Responsive Design**: Mobile-friendly interfaces

### Resource Management
- **Memory Optimization**: Efficient template rendering
- **Database Queries**: Optimized WHMCS database access
- **API Rate Limiting**: Respectful API usage
- **Auto-refresh**: Smart refresh intervals

## Troubleshooting

### Common Issues
1. **Service Not Provisioning**: Check API credentials and UUIDs
2. **Status Not Updating**: Verify API connectivity
3. **Templates Not Loading**: Check file permissions
4. **Logs Not Displaying**: Verify service UUID

### Debug Mode
Enable debug logging in WHMCS Module Logs for detailed troubleshooting information.

### Support Resources
- **WHMCS Documentation**: Module development guides
- **Coolify API Docs**: API reference and examples
- **n8n Documentation**: Workflow automation guides
- **Community Forums**: User support and discussions

## Version History

### v2.0.0 - Enhanced Templates
- Complete template system overhaul
- Enhanced client area dashboard
- Advanced admin interface
- Comprehensive user documentation
- Real-time status monitoring
- Performance metrics integration

### v1.0.0 - Initial Release
- Basic service provisioning
- Simple client/admin interfaces
- Core API integration
- PostgreSQL database support

## Contributing

### Template Development
1. Follow existing template structure
2. Maintain responsive design principles
3. Include proper error handling
4. Add comprehensive documentation

### Code Standards
- PSR-4 autoloading
- Proper error handling
- Security best practices
- Performance optimization

## License

This module is provided under the MIT License. See LICENSE file for details.

## Support

For technical support and feature requests:
- **Documentation**: Check this README and inline comments
- **Issues**: Report bugs and feature requests
- **Community**: Join discussions and share improvements
- **Professional Support**: Available for custom implementations
